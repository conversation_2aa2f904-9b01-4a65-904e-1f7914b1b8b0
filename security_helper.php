<?php
require_once ('pgr_sanitizer.php');

function sanitize_global_input(){
    if (!empty($_REQUEST)) {
        $_REQUEST = sanitize_input($_REQUEST, TRUE);
    }   

    if (!empty($_POST)) {
        $_POST= sanitize_input($_POST, TRUE);
    }

    if (!empty($_GET)) {
        $_GET= sanitize_input($_GET, TRUE);
    }

    if (!empty($_COOKIE)) {
        $_COOKIE= sanitize_input($_COOKIE, TRUE);
    }
}

function validate_pdf($file_name, $file_tmp, $max_size = 5242880) {
    $result = array('status' => false, 'message' => '');

    // 1. Validasi ekstensi .pdf
    $ext = strtolower(substr(strrchr($file_name, '.'), 1));
    if ($ext !== 'pdf') {
        $result['message'] = 'Ekstensi file bukan PDF.';
        return $result;
    }

    // 2. Validasi ukuran file
    $file_size = @filesize($file_tmp);
    if ($file_size === false) {
        $result['message'] = 'Gagal membaca ukuran file.';
        return $result;
    }

    if ($file_size > $max_size) {
        $result['message'] = 'Ukuran file melebihi batas maksimal.';
        return $result;
    }

    // 3. Validasi isi file: signature harus %PDF
    $handle = @fopen($file_tmp, 'rb');
    if (!$handle) {
        $result['message'] = 'Tidak dapat membuka file sementara.';
        return $result;
    }

    $signature = fread($handle, 4);
    fclose($handle);

    if ($signature !== '%PDF') {
        $result['message'] = 'File tidak memiliki signature PDF yang valid.';
        return $result;
    }

    // 4. Validasi nama file (tidak mengandung karakter berbahaya)
    if (preg_match('/[^a-zA-Z0-9_\-\.]/', $file_name)) {
        $result['message'] = 'Nama file mengandung karakter tidak valid.';
        return $result;
    }

    $result['status'] = true;
    $result['message'] = 'File PDF valid dan aman.';
    return $result;
}

function is_valid_date($date) {
  if (empty($date)) return true; // Empty dates are allowed
  if (!preg_match('/^\d{2}-\d{2}-\d{4}$/', $date)) return false;

  $parts = explode('-', $date);
  return checkdate($parts[1], $parts[0], $parts[2]);
}

function validate_and_sanitize_output($data, $context = 'html') {
    if (is_array($data)) {
        return array_map(function($item) use ($context) {
            return validate_and_sanitize_output($item, $context);
        }, $data);
    }

    switch ($context) {
        case 'html':
            return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        case 'url':
            return urlencode($data);
        case 'js':
            return json_encode($data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
        case 'css':
            return preg_replace('/[^a-zA-Z0-9\-_]/', '', $data);
        default:
            return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}

function log_security_event($event, $details = '') {
    // Ensure logs directory exists
    $log_dir = '../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0750, true);
    }

    // Sanitize details to prevent log injection
    $details = str_replace(array("\n", "\r", "\t"), ' ', $details);
    $details = substr($details, 0, 500); // Limit length

    $log_entry = sprintf(
        "[%s] IP:%s UA:%s EVENT:%s DETAILS:%s SESSION:%s\n",
        date('Y-m-d H:i:s'),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100),
        $event,
        $details,
        session_id() ?? 'none'
    );

    error_log($log_entry, 3, $log_dir . '/security.log');

    // Also log critical events to system log
    $critical_events = array('SQL_INJECTION_ATTEMPT', 'XSS_ATTEMPT', 'SESSION_HIJACK', 'CSRF_TOKEN_INVALID');
    if (in_array($event, $critical_events)) {
        error_log("CSMS Security Alert: $event - $details", 0);
    }
}

function validate_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }

    // Check token expiration (1 hour)
    if ((time() - $_SESSION['csrf_token_time']) > 3600) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

function sanitize_sap_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_sap_input', $input);
    }

    // Remove null bytes
    $input = str_replace(chr(0), '', $input);

    // Remove control characters except tab, newline, and carriage return
    $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);

    // Trim whitespace
    $input = trim($input);

    return $input;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) ||
        (time() - $_SESSION['csrf_token_time']) > 3600) { // Token expires after 1 hour
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

function secure_output($data) {
    if (is_array($data)) {
        return array_map('secure_output', $data);
    }
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}